package com.ejuetc.saasapi.sdk;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import feign.Feign;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;

import static com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.time.Duration.between;
import static java.time.LocalDateTime.now;
import static java.time.LocalDateTime.parse;
import static java.time.format.DateTimeFormatter.ofPattern;
import static java.util.stream.Collectors.toMap;

@Slf4j
public class SaaSApiSDK {
    //请求业务头信息
    public static final String HEADER_REQUEST_ID = "etc-request-id";
    public static final String HEADER_NOTIFY_ID = "etc-notify-id";
    public static final String HEADER_KEY_CODE = "etc-key-code";
    public static final String HEADER_API_CODE = "etc-api-code";
    public static final String HEADER_TIME = "etc-time";
    public static final String HEADER_SIGN = "etc-sign";

    //对内转发头信息
    public static final String HEADER_TRACE_NO = "etc-trace-no";
    public static final String HEADER_USER_ID = "etc-user-id";//所属用户ID

    //废弃字段
    public static final String HEADER_REQUEST_TIME = "etc-request-time";//废弃字段(替换为HEADER_TIME)
    public static final String HEADER_MERCHANT_ID = "etc-merchant-id";//废弃字段(替换为HEADER_USER_ID)

    //非参数常量
    public static final String ALGORITHM = "SHA-256";
    public static final String NOTIFY_SUCCESS = "RECEIVE_NOTIFY_SUCCESS";
    public static final DateTimeFormatter DATE_TIME_FORMATTER = ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final RestTemplate REST_TEMPLATE = new RestTemplate(List.of(new StringHttpMessageConverter(StandardCharsets.UTF_8)));

    private final String keyCode;
    private final String keySecret;
    private final String serverUrl;//服务端URL

    public SaaSApiSDK(String keyCode, String keySecret, String serverUrl) {
        this.keyCode = keyCode;
        this.keySecret = keySecret;
        this.serverUrl = serverUrl;
    }

    @SneakyThrows
    public ResponseEntity<String> uploadFile(String requestId, String filePath) {
        File file = new File(filePath);
        HttpHeaders headers = makeHeaders("uploadFile", requestId, file.getName());
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(file));
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);

        log.info("uploadFile request:\n{}", entity);
        ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(serverUrl, HttpMethod.POST, entity, String.class);
        log.info("uploadFile response:\n{}", responseEntity.getBody());
        return responseEntity;
    }

    @SneakyThrows
    public ResponseEntity<String> businessRequest(String apiCode, String requestId, String httpBody) {
        HttpHeaders headers = makeHeaders(apiCode, requestId, httpBody);
        HttpEntity<String> entity = new HttpEntity<>(httpBody, headers);
        log.info("businessRequest request:\n{}", entity);
        ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(serverUrl, HttpMethod.POST, entity, String.class);
        log.info("businessRequest response:\n{}", responseEntity.getBody());
        return responseEntity;
    }

    /**
     * FeignClient远程接口代理调用
     * 真正的FeignClient功能实现，与Gateway项目的GatewaySDK#feignClient方法兼容
     *
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @param interfaceClass 接口类全限定名
     * @param requestBody 请求体
     * @return 远程服务响应
     */
    @SneakyThrows
    public ResponseEntity<String> feignClient(String serviceName, String methodName, String interfaceClass, String requestBody) {
        // 构造特殊的apiCode来标识这是FeignClient调用
        String apiCode = "FEIGN_CLIENT:" + serviceName + ":" + methodName + ":" + interfaceClass;
        String requestId = java.util.UUID.randomUUID().toString();

        HttpHeaders headers = makeHeaders(apiCode, requestId, requestBody);
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
        log.info("feignClient(真正实现) request:\n{}", entity);
        ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(serverUrl, HttpMethod.POST, entity, String.class);
        log.info("feignClient(真正实现) response:\n{}", responseEntity.getBody());
        return responseEntity;
    }

    /**
     * FeignClient远程接口代理调用（简化版本，保持向后兼容）
     * 这个方法保持与原有代码的兼容性
     *
     * @param apiCode 接口代码
     * @param requestId 请求ID
     * @param httpBody 请求体
     * @return 远程服务响应
     */
    @SneakyThrows
    public ResponseEntity<String> feignClient(String apiCode, String requestId, String httpBody) {
        HttpHeaders headers = makeHeaders(apiCode, requestId, httpBody);
        HttpEntity<String> entity = new HttpEntity<>(httpBody, headers);
        log.info("feignClient(兼容版本) request:\n{}", entity);
        ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(serverUrl, HttpMethod.POST, entity, String.class);
        log.info("feignClient(兼容版本) response:\n{}", responseEntity.getBody());
        return responseEntity;
    }

    /**
     * FeignClient远程接口代理调用（对象参数重载）
     * Gateway项目新增功能
     *
     * @param apiCode 接口代码
     * @param requestId 请求ID
     * @param requestObject 请求对象
     * @return 远程服务响应
     */
    @SneakyThrows
    public ResponseEntity<String> feignClient(String apiCode, String requestId, Object requestObject) {
        String httpBody = requestObject == null ? "{}" :
                         (requestObject instanceof String ? (String) requestObject :
                          com.alibaba.fastjson.JSON.toJSONString(requestObject));
        return feignClient(apiCode, requestId, httpBody);
    }

    private HttpHeaders makeHeaders(String apiCode, String requestId, String httpBody) {
        Map<String, String> headersMap = new HashMap<>(Map.of(
                HEADER_API_CODE, apiCode,
                HEADER_REQUEST_ID, requestId,
                HEADER_KEY_CODE, keyCode,
                HEADER_TIME, now().format(DATE_TIME_FORMATTER)
        ));
        headersMap.put(HEADER_SIGN, calcSign(headersMap, httpBody, keySecret));
        log.info("httpHeader:\n{}", headersMap);

        HttpHeaders headers = new HttpHeaders();
        headers.setAll(headersMap);
        return headers;
    }

    @SneakyThrows
    public static String calcSign(Map<String, String> headers, String body, String key) {
        log.trace("1,原始数据 headers:{} body:{} key:{}", headers, body, key);
        // 1. 对除sign外所有http header参数，根据参数名称的ASCII码表的顺序排序
        Map<String, String> sortedHeaders = new TreeMap<>();
        headers.forEach((k, v) -> {
            if (k.startsWith("etc-") && !k.equals(HEADER_SIGN))
                sortedHeaders.put(k, v);
        });
        log.trace("2,排序headers:{}", sortedHeaders);

        // 2. 将排序好的参数名和参数值拼装在一起，再拼装http请求体和key密码
        StringBuilder signatureString = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedHeaders.entrySet()) {
            signatureString.append(entry.getKey()).append(entry.getValue());
        }
        signatureString.append(body).append(key);
        log.debug("3,报文拼接:{}", signatureString);

        // 3. 对拼装字符串计算签名: 取得utf-8字节码 -> SHA256摘要 -> base64编码
        MessageDigest digest = MessageDigest.getInstance(ALGORITHM);
        byte[] hash = digest.digest(signatureString.toString().getBytes(UTF_8));
        String sign = Base64.getEncoder().encodeToString(hash);
        log.debug("4,签名结果:{}", sign);
        return sign;
    }

    public static String checkSign(Map<String, String> headers, String body, String secret) {
        LocalDateTime time = parse(getTime(headers), DATE_TIME_FORMATTER);
        Duration duration = between(now(), time);
        if (Math.abs(duration.toSeconds()) > 300)
            return "请求已过期";

        String receiveSign = headers.get(HEADER_SIGN);
        String calcSign = calcSign(headers, body, secret);
        if (!calcSign.equals(receiveSign))
            return "签名验证失败";

        return null;
    }

    public static String getTime(Map<String, String> headers) {
        String time = headers.get(HEADER_TIME);
        if (time == null) time = headers.get(HEADER_REQUEST_TIME);
        return time;
    }

}